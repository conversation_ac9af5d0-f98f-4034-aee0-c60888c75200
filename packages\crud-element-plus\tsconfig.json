{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "dist", "strict": true, "noImplicitAny": false, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "jsx": "preserve", "jsxImportSource": "vue", "moduleResolution": "Node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "declaration": true, "emitDeclarationOnly": true, "forceConsistentCasingInFileNames": true, "target": "ESNext", "module": "ESNext", "types": ["node", "element-plus/global"]}, "include": ["src/**/*.ts", "src/**/*.vue", "src/**/*.d.ts"], "exclude": ["node_modules", "dist"]}
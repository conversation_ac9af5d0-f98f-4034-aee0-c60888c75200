{"name": "@northal/crud-element-plus", "version": "1.0.6", "description": "CRUD Element Plus 组件包", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./index.css": "./dist/index.css"}, "files": ["dist", "README.md"], "scripts": {"build": "vite build", "clean": "<PERSON><PERSON><PERSON> dist", "publish": "npm publish --access public"}, "repository": {"type": "git", "url": "git+https://github.com/North-al/north-crud.git"}, "homepage": "https://github.com/North-al/north-crud.git", "keywords": ["vue3", "element-plus", "crud", "table", "typescript"], "author": "North <<EMAIL>>", "license": "ISC", "packageManager": "pnpm@10.11.0", "peerDependencies": {"@element-plus/icons-vue": "^2.3.1", "element-plus": "^2.10.2", "vue": "^3.5.17"}, "devDependencies": {"@types/node": "^24.0.3"}, "dependencies": {"vue-draggable-next": "^2.2.1"}}
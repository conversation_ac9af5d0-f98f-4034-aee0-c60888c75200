{
    //   "extends": "@vue/tsconfig/tsconfig.dom.json",
    "extends": "../../tsconfig.base.json",
    "compilerOptions": {
        "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",

        "jsx": "preserve",
        "jsxImportSource": "vue",
        /* Linting */
        "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "erasableSyntaxOnly": true,
        "noFallthroughCasesInSwitch": true,
        "noUncheckedSideEffectImports": true,
        "baseUrl": ".",
        "paths": {
            "@core/*": ["../crud-core/src/*"],
            "@el/*": ["../crud-element-plus/src/*"]
        }
    },
    "include": [
        "src/**/*.ts",
        "src/**/*.tsx",
        "src/**/*.vue",
        "../crud-core/src/**/*.ts",
        "../crud-element-plus/src/**/*.ts",
        "../crud-element-plus/src/**/*.vue"
    ]
}

{"name": "@northal/crud-core", "version": "1.0.0", "description": "North CRUD 核心包，提供类型定义和核心逻辑", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"build": "rollup -c rollup.config.ts --configPlugin typescript", "clean": "<PERSON><PERSON><PERSON> dist"}, "repository": {"type": "git", "url": "https://github.com/North-al/north-crud.git"}, "homepage": "https://github.com/North-al/north-crud.git", "keywords": ["crud", "vue3", "useCrud", "core"], "author": "North <<EMAIL>>", "license": "ISC", "packageManager": "pnpm@10.11.0", "peerDependencies": {"vue": "^3.5.17"}, "devDependencies": {"vue": "^3.5.17"}}
{"name": "playground", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.0", "@northal/crud-element-plus": "workspace:*", "@types/node": "^24.0.3", "element-plus": "^2.10.2", "vue": "^3.5.13"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}